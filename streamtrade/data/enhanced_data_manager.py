"""
Enhanced Data Manager for Lionaire Platform.
Implements N Days Back loading strategy and session-aware timeframe conversion.
"""

import pandas as pd
import numpy as np
from typing import Optional, Dict, List, Tuple, Any
from datetime import datetime, timedelta
import threading
from pathlib import Path

from ..config.settings import settings
from ..config.logging_config import get_logger, log_performance
from ..config.user_settings import get_user_settings
from ..core.utils import memory_usage, estimate_memory_usage
from .data_loader import DataLoader
from .session_aware_converter import SessionAwareConverter
from ..cache.disk_cache import SmartDiskCache
from ..cache.indicator_cache import IndicatorCache
from ..cache.invalidation_manager import get_invalidation_manager, SmartInvalidationManager
from ..cache.impact_manager import ChangeType

logger = get_logger(__name__)


class EnhancedDataManager:
    """
    Enhanced data management system with session-aware timeframe conversion and smart disk cache.

    Features:
    - N Days Back loading strategy (replaces "Last N Candles")
    - Session-aware timeframe conversion
    - Smart Disk Cache with Parquet-based storage
    - LRU eviction and cache persistence
    - Per-indicator caching with style separation
    - Memory management with user-configurable limits
    - Thread-safe operations
    """

    def __init__(self):
        self.data_loader = DataLoader()
        self.session_converter = SessionAwareConverter()
        self.user_settings = get_user_settings()

        # Cache settings from user preferences
        cache_settings = self.user_settings.get_cache_settings()
        data_settings = self.user_settings.get_data_loading_settings()

        self.max_cache_size_mb = cache_settings.get('max_cache_size_gb', 10) * 1024  # Convert GB to MB
        self.max_candles_load = data_settings.get('max_candles_load', 200000)
        self.max_candles_display = data_settings.get('max_candles_display', 15000)
        self.enable_disk_cache = cache_settings.get('enable_disk_cache', True)

        # Initialize Smart Disk Cache System (Phase 5.4)
        if self.enable_disk_cache:
            self.disk_cache = SmartDiskCache(max_size_gb=cache_settings.get('max_cache_size_gb', 10))
            self.indicator_cache = IndicatorCache(self.disk_cache)
            logger.info("Smart Disk Cache System enabled")
        else:
            self.disk_cache = None
            self.indicator_cache = None
            logger.info("Smart Disk Cache System disabled - using memory cache only")

        # Initialize Smart Invalidation Manager (Phase 5.5.2)
        self.invalidation_manager = get_invalidation_manager(self.disk_cache, self.indicator_cache)
        logger.info("Smart Invalidation Manager initialized")

        # Legacy memory cache for backward compatibility and fallback
        self._data_cache = {}
        self._cache_lock = threading.Lock()
        
        # M1 base data cache for efficient timeframe switching
        self._m1_base_cache = {}  # {pair: {'data': DataFrame, 'timestamp': datetime, 'size_mb': float}}
        
        # User request context for preserving data range across timeframe switches
        self._user_context = {}  # {pair: {'days_back': int, 'start_date': datetime, 'end_date': datetime}}
        
        logger.info("EnhancedDataManager initialized with session-aware conversion")
    
    def load_n_days_back(self, pair: str, timeframe: str, days_back: int = None) -> Optional[pd.DataFrame]:
        """
        Load data based on N days back instead of candle count.
        
        Args:
            pair: Currency pair symbol
            timeframe: Target timeframe
            days_back: Number of days to load back (uses user setting if None)
            
        Returns:
            DataFrame with requested data or None if error
        """
        try:
            # Use user setting if days_back not specified
            if days_back is None:
                days_back = self.user_settings.get('data_loading.days_back_default', 5)
            
            # Validate minimum days
            min_days = self.user_settings.get('data_loading.days_back_minimum', 1)
            days_back = max(days_back, min_days)
            
            # Calculate date range - use latest available data date
            try:
                # Get the actual latest available date from data files
                date_range = self.data_loader.get_available_date_range(pair)
                if date_range:
                    _, end_date = date_range
                    # Use the actual latest date from files
                    logger.debug(f"Using latest available date for {pair}: {end_date}")
                else:
                    # Fallback to current date if no data found
                    end_date = datetime.now()
                    logger.warning(f"No data files found for {pair}, using current date")
            except Exception as e:
                # Fallback to current date on error
                end_date = datetime.now()
                logger.warning(f"Error getting date range for {pair}: {e}, using current date")

            start_date = end_date - timedelta(days=days_back)

            # 🔍 DEBUG LOGGING - Initial Load
            print(f"\n{'='*60}")
            print(f"🔄 INITIAL DATA LOADING")
            print(f"{'='*60}")
            print(f"📊 Pair: {pair}")
            print(f"⏰ Timeframe: {timeframe}")
            print(f"📅 Days Back: {days_back}")
            print(f"📅 Date Range: {start_date.date()} to {end_date.date()}")

            logger.info(f"Loading {days_back} days back for {pair} ({timeframe}): {start_date.date()} to {end_date.date()}")

            # Store user context for timeframe switching
            self._user_context[pair] = {
                'days_back': days_back,
                'start_date': start_date,
                'end_date': end_date
            }

            result = self.load_data_range(pair, timeframe, start_date, end_date)

            # Check if cache_all_tf_on_load is enabled
            if result is not None and self.user_settings.get('data_loading.cache_all_tf_on_load', False):
                self._cache_all_timeframes_background(pair, start_date, end_date)

            return result

        except Exception as e:
            logger.error(f"Error loading {days_back} days back for {pair}: {e}")
            return None
    
    @log_performance
    def load_data_range(self, pair: str, timeframe: str, start_date: datetime, end_date: datetime) -> Optional[pd.DataFrame]:
        """
        Load data for specific date range with session-aware conversion.
        
        Args:
            pair: Currency pair symbol
            timeframe: Target timeframe
            start_date: Start date
            end_date: End date
            
        Returns:
            DataFrame with requested data or None if error
        """
        try:
            # Check if timeframe is enabled
            if not self.user_settings.is_timeframe_enabled(timeframe):
                logger.warning(f"Timeframe {timeframe} is disabled in user settings")
                return None
            
            # Generate cache key
            cache_key = f"{pair}_{timeframe}_{start_date.date()}_{end_date.date()}"

            # Check disk cache first (Phase 5.4)
            if self.disk_cache and timeframe != 'M1':
                # For non-M1 timeframes, check if we have cached conversion
                m1_cache_key = self.disk_cache.find_m1_cache(pair, start_date, end_date)
                if m1_cache_key:
                    tf_cache_key = self.disk_cache.find_timeframe_cache(pair, timeframe, m1_cache_key)
                    if tf_cache_key:
                        cached_data = self.disk_cache.load_data(tf_cache_key)
                        if cached_data is not None:
                            logger.debug(f"Using disk cached {timeframe} data for {pair}")

                            # 🔍 DEBUG LOGGING - Disk Cache Hit
                            limited_data = self._limit_display_candles(cached_data)
                            print(f"💾 Data Source: DISK CACHE")
                            print(f"📊 Converted Candles: {len(cached_data)}")
                            print(f"📺 Displayed Candles: {len(limited_data)}")
                            print(f"{'='*60}")

                            # Store user context even for cached data
                            self._user_context[pair] = {
                                'start_date': start_date,
                                'end_date': end_date,
                                'last_timeframe': timeframe
                            }

                            return limited_data

            # Check memory cache
            cached_data = self._get_from_cache(cache_key)
            if cached_data is not None:
                logger.debug(f"Using memory cached data for {cache_key}")

                # 🔍 DEBUG LOGGING - Memory Cache Hit
                limited_data = self._limit_display_candles(cached_data)
                print(f"🧠 Data Source: MEMORY CACHE")
                print(f"📊 Converted Candles: {len(cached_data)}")
                print(f"📺 Displayed Candles: {len(limited_data)}")
                print(f"{'='*60}")

                # Store user context even for cached data
                self._user_context[pair] = {
                    'start_date': start_date,
                    'end_date': end_date,
                    'last_timeframe': timeframe
                }

                return limited_data

            # Load M1 base data
            m1_data = self._load_m1_base_data(pair, start_date, end_date)
            if m1_data is None or m1_data.empty:
                logger.warning(f"No M1 data available for {pair} in range {start_date.date()} to {end_date.date()}")
                return None

            # 🔍 DEBUG LOGGING - Fresh Data Loading
            print(f"📁 Data Source: FRESH LOAD FROM FILES")
            print(f"📊 M1 Candles Loaded: {len(m1_data)}")

            # Convert to target timeframe if needed
            if timeframe == 'M1':
                result_data = m1_data
                m1_cache_key = None  # M1 data doesn't need conversion
                print(f"📊 Converted Candles: {len(result_data)} (no conversion needed)")
            else:
                result_data = self.session_converter.convert_timeframe(m1_data, timeframe, pair)
                if result_data is None:
                    logger.error(f"Failed to convert {pair} to {timeframe}")
                    return None

                print(f"📊 Converted Candles: {len(result_data)}")

                # Get M1 cache key for timeframe caching
                m1_cache_key = self.disk_cache.find_m1_cache(pair, start_date, end_date) if self.disk_cache else None

            # Cache the result (both disk and memory)
            self._cache_timeframe_data(cache_key, result_data, pair, timeframe, m1_cache_key)

            # Store user context for timeframe switching
            self._user_context[pair] = {
                'start_date': start_date,
                'end_date': end_date,
                'last_timeframe': timeframe
            }

            # Apply display limit
            limited_data = self._limit_display_candles(result_data)

            # 🔍 DEBUG LOGGING - Final Result
            print(f"📺 Displayed Candles: {len(limited_data)}")
            print(f"{'='*60}")

            logger.info(f"Loaded {len(result_data)} {timeframe} candles for {pair} (displaying {len(limited_data)})")
            return limited_data
            
        except Exception as e:
            logger.error(f"Error loading data range for {pair}: {e}")
            return None
    
    def _load_m1_base_data(self, pair: str, start_date: datetime, end_date: datetime) -> Optional[pd.DataFrame]:
        """
        Load M1 base data with intelligent caching (Smart Disk Cache + Memory fallback).

        Args:
            pair: Currency pair symbol
            start_date: Start date
            end_date: End date

        Returns:
            M1 DataFrame or None if error
        """
        try:
            # Try Smart Disk Cache first (Phase 5.4)
            if self.disk_cache:
                disk_cache_key = self.disk_cache.find_m1_cache(pair, start_date, end_date)
                if disk_cache_key:
                    cached_data = self.disk_cache.load_data(disk_cache_key)
                    if cached_data is not None:
                        # Filter to requested range
                        mask = (cached_data.index >= start_date) & (cached_data.index <= end_date)
                        filtered_data = cached_data[mask]
                        logger.debug(f"Using disk cached M1 data for {pair} (key: {disk_cache_key})")
                        return filtered_data

            # Fallback to memory cache
            m1_cache_key = f"{pair}_M1"
            with self._cache_lock:
                if m1_cache_key in self._m1_base_cache:
                    cached_m1 = self._m1_base_cache[m1_cache_key]
                    cached_data = cached_m1['data']

                    # Check if cached data covers the requested range
                    if (not cached_data.empty and
                        cached_data.index[0] <= start_date and
                        cached_data.index[-1] >= end_date):

                        # Filter to requested range
                        mask = (cached_data.index >= start_date) & (cached_data.index <= end_date)
                        filtered_data = cached_data[mask]

                        logger.debug(f"Using memory cached M1 data for {pair}")
                        return filtered_data

            # Load fresh M1 data from files
            logger.info(f"Loading fresh M1 data for {pair}")
            m1_data = self.data_loader.load_data_range(pair, start_date, end_date, max_rows=self.max_candles_load)

            if m1_data is not None and not m1_data.empty:
                # Cache M1 data for future use
                self._cache_m1_base_data(pair, m1_data, start_date, end_date)

                # Filter to requested range
                mask = (m1_data.index >= start_date) & (m1_data.index <= end_date)
                filtered_data = m1_data[mask]

                return filtered_data

            return None

        except Exception as e:
            logger.error(f"Error loading M1 base data for {pair}: {e}")
            return None
    
    def _cache_m1_base_data(self, pair: str, m1_data: pd.DataFrame, start_date: datetime, end_date: datetime):
        """Cache M1 base data for efficient timeframe switching (Smart Disk Cache + Memory fallback)."""
        try:
            # Store in Smart Disk Cache first (Phase 5.4)
            if self.disk_cache:
                disk_cache_key = self.disk_cache.store_m1_data(pair, m1_data, start_date, end_date)
                if disk_cache_key:
                    logger.debug(f"Stored M1 data in disk cache: {pair} -> {disk_cache_key}")
                else:
                    logger.warning(f"Failed to store M1 data in disk cache for {pair}")

            # Also store in memory cache for immediate access
            cache_key = f"{pair}_M1"
            memory_info = estimate_memory_usage(m1_data)
            data_size_mb = memory_info.get('total_mb', 0)

            with self._cache_lock:
                self._m1_base_cache[cache_key] = {
                    'data': m1_data.copy(),
                    'timestamp': datetime.now(),
                    'size_mb': data_size_mb
                }

                # Check cache size and cleanup if needed
                self._cleanup_cache_if_needed()

            logger.debug(f"Cached {len(m1_data)} M1 candles for {pair} ({data_size_mb:.1f} MB)")

        except Exception as e:
            logger.error(f"Error caching M1 data for {pair}: {e}")

    def _cache_timeframe_data(self, cache_key: str, data: pd.DataFrame, pair: str,
                            timeframe: str, m1_cache_key: Optional[str]):
        """Cache timeframe data in both disk and memory cache."""
        try:
            # Store in disk cache if available and not M1
            if self.disk_cache and timeframe != 'M1' and m1_cache_key:
                disk_cache_key = self.disk_cache.store_timeframe_data(pair, timeframe, data, m1_cache_key)
                if disk_cache_key:
                    logger.debug(f"Stored {timeframe} data in disk cache: {pair} -> {disk_cache_key}")

            # Store in memory cache
            self._add_to_cache(cache_key, data)

        except Exception as e:
            logger.error(f"Error caching {timeframe} data for {pair}: {e}")

    def switch_timeframe(self, pair: str, new_timeframe: str) -> Optional[pd.DataFrame]:
        """
        Switch timeframe using cached M1 data and preserved user context.
        
        Args:
            pair: Currency pair symbol
            new_timeframe: Target timeframe
            
        Returns:
            DataFrame with new timeframe data or None if error
        """
        try:
            # Check if timeframe is enabled
            if not self.user_settings.is_timeframe_enabled(new_timeframe):
                logger.warning(f"Timeframe {new_timeframe} is disabled in user settings")
                return None
            
            # Get user context for this pair
            if pair not in self._user_context:
                logger.warning(f"No user context found for {pair}, using default days back")
                return self.load_n_days_back(pair, new_timeframe)

            context = self._user_context[pair]
            start_date = context['start_date']
            end_date = context['end_date']

            # 🔍 DEBUG LOGGING - Timeframe Switch
            print(f"\n{'='*60}")
            print(f"🔄 TIMEFRAME SWITCHING")
            print(f"{'='*60}")
            print(f"📊 Pair: {pair}")
            print(f"⏰ New Timeframe: {new_timeframe}")
            print(f"📅 Using Cached Date Range: {start_date.date()} to {end_date.date()}")

            logger.info(f"Switching {pair} to {new_timeframe} using cached data range")

            return self.load_data_range(pair, new_timeframe, start_date, end_date)
            
        except Exception as e:
            logger.error(f"Error switching timeframe for {pair}: {e}")
            return None
    
    def _limit_display_candles(self, data: pd.DataFrame) -> pd.DataFrame:
        """
        Limit displayed candles based on user settings.
        
        Args:
            data: Input DataFrame
            
        Returns:
            Limited DataFrame showing most recent candles
        """
        if data.empty:
            return data
        
        if len(data) <= self.max_candles_display:
            return data
        
        # Show most recent candles
        limited_data = data.tail(self.max_candles_display)
        
        logger.debug(f"Limited display from {len(data)} to {len(limited_data)} candles")
        return limited_data
    
    def get_cached_data(self, pair: str, timeframe: str) -> Optional[pd.DataFrame]:
        """
        Public method to get cached data for chart restoration.

        Args:
            pair: Currency pair
            timeframe: Timeframe

        Returns:
            Cached DataFrame if available, None otherwise
        """
        try:
            # Try disk cache first
            if self.disk_cache:
                # Look for recent M1 cache entries
                from datetime import datetime, timedelta
                end_date = datetime.now()
                start_date = end_date - timedelta(days=30)  # Look back 30 days

                m1_cache_key = self.disk_cache.find_m1_cache(pair, start_date, end_date)
                if m1_cache_key:
                    if timeframe == 'M1':
                        cached_data = self.disk_cache.load_data(m1_cache_key, 'm1_base')
                        if cached_data is not None:
                            logger.debug(f"Found cached M1 data for {pair}")
                            return self._limit_display_candles(cached_data)
                    else:
                        tf_cache_key = self.disk_cache.find_timeframe_cache(pair, timeframe, m1_cache_key)
                        if tf_cache_key:
                            cached_data = self.disk_cache.load_data(tf_cache_key, 'timeframes')
                            if cached_data is not None:
                                logger.debug(f"Found cached {timeframe} data for {pair}")
                                return self._limit_display_candles(cached_data)

            # Try memory cache
            cache_key = f"{pair}_{timeframe}_recent"
            cached_data = self._get_from_cache(cache_key)
            if cached_data is not None:
                logger.debug(f"Found memory cached data for {pair} {timeframe}")
                return self._limit_display_candles(cached_data)

            # Try alternative cache keys
            for days_back in [5, 10, 30]:
                cache_key = f"{pair}_{timeframe}_{days_back}d"
                cached_data = self._get_from_cache(cache_key)
                if cached_data is not None:
                    logger.debug(f"Found memory cached data for {pair} {timeframe} ({days_back}d)")
                    return self._limit_display_candles(cached_data)

            return None

        except Exception as e:
            logger.debug(f"Error getting cached data for {pair} {timeframe}: {e}")
            return None

    def _get_from_cache(self, cache_key: str) -> Optional[pd.DataFrame]:
        """Get data from cache if available and valid."""
        with self._cache_lock:
            if cache_key in self._data_cache:
                cached_item = self._data_cache[cache_key]

                # Check if cache is still valid (within timeout)
                cache_age = datetime.now() - cached_item['timestamp']
                cache_timeout = timedelta(minutes=30)  # 30 minutes timeout

                if cache_age < cache_timeout:
                    return cached_item['data'].copy()
                else:
                    # Remove expired cache
                    del self._data_cache[cache_key]

        return None
    
    def _add_to_cache(self, cache_key: str, data: pd.DataFrame):
        """Add data to cache."""
        try:
            memory_info = estimate_memory_usage(data)
            data_size_mb = memory_info.get('total_mb', 0)

            with self._cache_lock:
                self._data_cache[cache_key] = {
                    'data': data.copy(),
                    'timestamp': datetime.now(),
                    'size_mb': data_size_mb
                }
                
                # Check cache size and cleanup if needed
                self._cleanup_cache_if_needed()
                
        except Exception as e:
            logger.error(f"Error adding to cache: {e}")
    
    def _cleanup_cache_if_needed(self):
        """Cleanup cache if it exceeds size limits."""
        try:
            # Calculate total cache size
            total_size_mb = sum(item['size_mb'] for item in self._data_cache.values())
            total_size_mb += sum(item['size_mb'] for item in self._m1_base_cache.values())
            
            if total_size_mb > self.max_cache_size_mb:
                logger.info(f"Cache size ({total_size_mb:.1f} MB) exceeds limit ({self.max_cache_size_mb:.1f} MB), cleaning up")
                
                # Remove oldest entries from data cache
                if self._data_cache:
                    sorted_items = sorted(self._data_cache.items(), key=lambda x: x[1]['timestamp'])
                    items_to_remove = len(sorted_items) // 4  # Remove 25% of items
                    
                    for i in range(items_to_remove):
                        cache_key = sorted_items[i][0]
                        del self._data_cache[cache_key]
                
                logger.info(f"Cache cleanup completed")
                
        except Exception as e:
            logger.error(f"Error during cache cleanup: {e}")
    
    def clear_cache(self):
        """Clear all caches."""
        with self._cache_lock:
            self._data_cache.clear()
            self._m1_base_cache.clear()
            self._user_context.clear()
        
        # Clear session converter cache
        self.session_converter.clear_cache()
        
        logger.info("All caches cleared")
    
    def get_cache_info(self) -> Dict[str, Any]:
        """Get comprehensive cache information and statistics (Memory + Disk)."""
        with self._cache_lock:
            # Memory cache stats
            data_cache_size = sum(item['size_mb'] for item in self._data_cache.values())
            m1_cache_size = sum(item['size_mb'] for item in self._m1_base_cache.values())
            total_memory_cache_size = data_cache_size + m1_cache_size

            memory_stats = {
                'data_cache_entries': len(self._data_cache),
                'data_cache_size_mb': data_cache_size,
                'm1_cache_entries': len(self._m1_base_cache),
                'm1_cache_size_mb': m1_cache_size,
                'total_cache_size_mb': total_memory_cache_size,
                'max_cache_size_mb': self.max_cache_size_mb,
                'cache_utilization_percent': (total_memory_cache_size / self.max_cache_size_mb) * 100,
                'user_contexts': len(self._user_context),
                'session_converter_cache': self.session_converter.get_cache_info()
            }

            # Disk cache stats (Phase 5.4)
            disk_stats = {}
            if self.disk_cache:
                disk_stats = self.disk_cache.get_stats()

            # Indicator cache stats
            indicator_stats = {}
            if self.indicator_cache:
                indicator_stats = self.indicator_cache.get_indicator_stats()

            return {
                'memory_cache': memory_stats,
                'disk_cache': disk_stats,
                'indicator_cache': indicator_stats,
                'disk_cache_enabled': self.enable_disk_cache
            }
    
    def get_insufficient_data_behavior(self) -> str:
        """Get user preference for insufficient data handling."""
        return self.user_settings.get('cache.insufficient_data_behavior', 'show_warning')
    
    def update_user_settings(self):
        """Update internal settings from user preferences."""
        cache_settings = self.user_settings.get_cache_settings()
        data_settings = self.user_settings.get_data_loading_settings()

        self.max_cache_size_mb = cache_settings.get('max_cache_size_gb', 10) * 1024
        self.max_candles_load = data_settings.get('max_candles_load', 200000)
        self.max_candles_display = data_settings.get('max_candles_display', 15000)
        self.enable_disk_cache = cache_settings.get('enable_disk_cache', True)

        logger.info("User settings updated in EnhancedDataManager")

    def _cache_all_timeframes_background(self, pair: str, start_date: datetime, end_date: datetime):
        """Cache all enabled timeframes in background for faster switching."""
        try:
            enabled_timeframes = self.user_settings.get('data_loading.enabled_timeframes', ['M1', 'M5', 'M15', 'H1', 'H4', 'D1'])

            def cache_worker():
                logger.info(f"Background caching all timeframes for {pair}")
                for tf in enabled_timeframes:
                    try:
                        # Skip if already cached
                        cache_key = f"{pair}_{tf}_{start_date.date()}_{end_date.date()}"
                        if self.disk_cache and self.disk_cache.find_timeframe_cache(pair, tf, cache_key):
                            continue

                        # Load and cache the timeframe
                        data = self.load_data_range(pair, tf, start_date, end_date)
                        if data is not None:
                            logger.debug(f"Background cached {tf} for {pair}: {len(data)} candles")

                    except Exception as e:
                        logger.warning(f"Failed to background cache {tf} for {pair}: {e}")

                logger.info(f"Background caching completed for {pair}")

            # Run in background thread
            import threading
            thread = threading.Thread(target=cache_worker, daemon=True)
            thread.start()

        except Exception as e:
            logger.error(f"Error starting background cache for {pair}: {e}")

    # ===== INDICATOR CACHE METHODS (Phase 5.4) =====

    def cache_indicator_calculation(self, indicator_name: str, params: Dict[str, Any],
                                  data_cache_key: str, result: pd.DataFrame) -> Optional[str]:
        """Cache indicator calculation results."""
        if self.indicator_cache:
            return self.indicator_cache.store_indicator_calculation(
                indicator_name, params, data_cache_key, result
            )
        return None

    def load_indicator_calculation(self, indicator_name: str, params: Dict[str, Any],
                                 data_cache_key: str) -> Optional[pd.DataFrame]:
        """Load cached indicator calculation."""
        if self.indicator_cache:
            return self.indicator_cache.load_indicator_calculation(
                indicator_name, params, data_cache_key
            )
        return None

    def cache_indicator_style(self, indicator_name: str, style_config: Dict[str, Any]) -> Optional[str]:
        """Cache indicator style configuration."""
        if self.indicator_cache:
            return self.indicator_cache.store_indicator_style(indicator_name, style_config)
        return None

    def load_indicator_style(self, indicator_name: str, style_config: Dict[str, Any]) -> Optional[Dict[str, Any]]:
        """Load cached indicator style configuration."""
        if self.indicator_cache:
            return self.indicator_cache.load_indicator_style(indicator_name, style_config)
        return None

    def invalidate_indicator_cache(self, indicator_name: str, data_cache_key: str) -> int:
        """Invalidate cached calculations for specific indicator."""
        if self.indicator_cache:
            return self.indicator_cache.invalidate_indicator(indicator_name, data_cache_key)
        return 0

    def invalidate_all_indicators(self, data_cache_key: str) -> int:
        """Invalidate all indicator calculations for specific data."""
        if self.indicator_cache:
            return self.indicator_cache.invalidate_all_indicators(data_cache_key)
        return 0

    # ===== CACHE MAINTENANCE METHODS (Phase 5.4) =====

    def perform_cache_maintenance(self, max_age_days: int = 30) -> Dict[str, Any]:
        """Perform comprehensive cache maintenance."""
        maintenance_results = {
            'memory_cleanup': False,
            'disk_maintenance': {},
            'indicator_cleanup': {}
        }

        try:
            # Memory cache cleanup
            self._cleanup_cache_if_needed()
            maintenance_results['memory_cleanup'] = True

            # Disk cache maintenance
            if self.disk_cache:
                disk_results = self.disk_cache.maintenance(max_age_days)
                maintenance_results['disk_maintenance'] = disk_results

            # Indicator cache cleanup (optional)
            if self.indicator_cache:
                indicator_results = self.indicator_cache.cleanup_indicator_cache()
                maintenance_results['indicator_cleanup'] = indicator_results

            logger.info("Cache maintenance completed successfully")

        except Exception as e:
            logger.error(f"Error during cache maintenance: {e}")

        return maintenance_results

    # ===== SMART INVALIDATION METHODS (Phase 5.5.2) =====

    def invalidate_cache_smart(self, change_type: ChangeType, affected_indicators: set = None,
                              metadata: Dict[str, Any] = None) -> Dict[str, Any]:
        """
        Perform smart cache invalidation based on change analysis.

        Args:
            change_type: Type of change that triggered invalidation
            affected_indicators: Set of indicators affected by the change
            metadata: Additional metadata about the change

        Returns:
            Dictionary with invalidation results
        """
        try:
            from ..cache.impact_manager import ChangeEvent

            # Create change event
            change_event = ChangeEvent(
                change_type=change_type,
                timestamp=datetime.now(),
                affected_indicators=affected_indicators or set(),
                metadata=metadata or {}
            )

            # Perform smart invalidation
            invalidation_plan = self.invalidation_manager.invalidate_based_on_change(change_event)

            return {
                'success': True,
                'scope': invalidation_plan.scope.value,
                'affected_indicators': len(invalidation_plan.affected_indicators),
                'affected_data_keys': len(invalidation_plan.affected_data_keys),
                'affected_style_keys': len(invalidation_plan.affected_style_keys),
                'reason': invalidation_plan.reason
            }

        except Exception as e:
            logger.error(f"Error in smart cache invalidation: {e}")
            return {
                'success': False,
                'error': str(e),
                'fallback': 'Safe invalidation performed'
            }

    def invalidate_on_data_reload(self, pair: str, timeframe: str) -> Dict[str, Any]:
        """Invalidate cache when data is reloaded."""
        return self.invalidate_cache_smart(
            change_type=ChangeType.DATA_RELOAD,
            metadata={'pair': pair, 'timeframe': timeframe}
        )

    def invalidate_on_timeframe_switch(self, pair: str, old_timeframe: str, new_timeframe: str) -> Dict[str, Any]:
        """Invalidate cache when timeframe is switched."""
        return self.invalidate_cache_smart(
            change_type=ChangeType.TIMEFRAME_SWITCH,
            metadata={
                'pair': pair,
                'old_timeframe': old_timeframe,
                'new_timeframe': new_timeframe
            }
        )

    def get_invalidation_statistics(self) -> Dict[str, Any]:
        """Get smart invalidation statistics."""
        try:
            return self.invalidation_manager.get_invalidation_statistics()
        except Exception as e:
            logger.error(f"Error getting invalidation statistics: {e}")
            return {'error': str(e)}

    def clear_all_caches(self, include_disk: bool = True):
        """Clear all caches (memory + disk + indicators)."""
        try:
            # Clear memory caches
            self.clear_cache()

            # Clear disk cache
            if include_disk and self.disk_cache:
                self.disk_cache.clear_cache()
                logger.info("Disk cache cleared")

            # Clear indicator cache
            if include_disk and self.indicator_cache:
                self.indicator_cache.cleanup_indicator_cache()
                logger.info("Indicator cache cleared")

            logger.info("All caches cleared successfully")

        except Exception as e:
            logger.error(f"Error clearing all caches: {e}")

    def get_disk_cache_stats(self) -> Dict[str, Any]:
        """Get detailed disk cache statistics."""
        if self.disk_cache:
            return self.disk_cache.get_stats()
        return {'error': 'Disk cache not enabled'}

    def get_indicator_cache_stats(self) -> Dict[str, Any]:
        """Get detailed indicator cache statistics."""
        if self.indicator_cache:
            return self.indicator_cache.get_indicator_stats()
        return {'error': 'Indicator cache not enabled'}

    # ===== DATA ACCESS METHODS =====

    def get_available_pairs(self) -> List[str]:
        """Get list of available currency pairs."""
        try:
            # Scan histdata directory for available pairs
            histdata_dir = self.data_loader.histdata_dir
            if not histdata_dir.exists():
                logger.warning(f"Histdata directory not found: {histdata_dir}")
                return []

            pairs = []
            for pair_dir in histdata_dir.iterdir():
                if pair_dir.is_dir():
                    # Check if directory has data files
                    files_info = self.data_loader.discover_files(pair_dir.name.upper())
                    if files_info:
                        pairs.append(pair_dir.name.upper())

            pairs.sort()
            logger.debug(f"Found {len(pairs)} available pairs: {pairs}")
            return pairs

        except Exception as e:
            logger.error(f"Error getting available pairs: {e}")
            return ['EURUSD', 'GBPUSD', 'USDJPY', 'AUDUSD']  # Fallback

    def get_available_timeframes(self) -> List[str]:
        """Get list of available timeframes based on user settings."""
        # Get enabled timeframes from user settings
        enabled_timeframes = self.user_settings.get('data_loading.enabled_timeframes',
                                                   ['M1', 'M5', 'M15', 'H1', 'H4', 'D1'])
        return enabled_timeframes

    def get_user_context(self, pair: str) -> Dict[str, Any]:
        """Get user context for specific pair."""
        return self._user_context.get(pair, {})
